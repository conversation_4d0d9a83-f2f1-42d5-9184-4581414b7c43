# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

```bash
# Development
npm run dev    # Start dev server on fe.rocketbird.cn:8089

# Build
npm run build  # Production build
npm run cdn    # Production build with CDN resources

# Code Quality
npm run lint   # ESLint code checking
npm run test:unit  # Run Vue CLI unit tests
```

## Architecture Overview

This is a Vue 2.7 SaaS fitness/gym management system with microfrontend capabilities via qiankun. Key architectural patterns:

### Multi-Page Application Structure
- Pages auto-detected from `src/pages/*/main.js` pattern
- Each page can have its own HTML template (`index.html` or fallback to `_default.html`)
- Configured as UMD library for microfrontend integration

### Route Organization
Routes are split across modules in `src/router/`:
- `index.js` - Main router with core routes
- `stat.js`, `invoice.js`, `management.js`, `settings.js`, `course.js`, `marketing.js` - Feature-specific routes
- `alipay_2024.js` - Payment integration routes

### State Management Pattern
Vuex store organized in modules (`src/store/modules/`):
- `user.js` - Authentication and user state  
- `employee.js` - Staff management
- `goods.js` - Inventory and products
- `pay.js` - Payment processing
- `websocket.js` - Real-time communication
- `diy.js` - UI customization

### Component Architecture
- **Global Components**: Auto-registered from `src/components/global/` (e.g., FaIcon for FontAwesome)
- **Form Components**: Rich form controls in `src/components/form/` (Editor, cropperPlus, payment selectors)
- **Business Components**: Domain-specific components (cardSelect, employeeNav, member, reservation)

### Key Development Patterns

**Route Metadata System**:
```javascript
{
  path: 'example',
  meta: {
    keepAlive: true,        // Enable Vue keep-alive caching
    hideBread: true,        // Hide breadcrumb
    hideSidebar: true,      // Hide sidebar
    parentName: 'Parent',   // For menu highlighting
    breadText: 'Custom'     // Custom breadcrumb text
  }
}
```

**AJAX Loading Control**:
```javascript
// Disable global loading indicator
this.$service.post(url, data, { loading: false })
```

**Page State Persistence**:
Use `keepAlive: true` in route meta, then use `activated()` hook instead of `created()` for data refresh.

### Build Configuration Highlights
- **Webpack Aliases**: `@` (src), `components`, `views`, `utils`, `mixins`, `store`, `router`
- **CDN Support**: Configurable via build command argument
- **Proxy Setup**: `/api` → `https://beta.rocketbird.cn` in development
- **Multi-entry Detection**: Automatic page discovery and configuration

### UI Framework Stack
- **iView 3.4.2**: Primary component library
- **FontAwesome 4.7.0**: Icon system
- **Less**: CSS preprocessing with JavaScript enabled
- **Vue 2.7**: Core framework with Composition API support

### Service Layer
Centralized API handling in `src/service/getData.js` with request/response interceptors for loading states and error handling.

### Modal Guidelines
From README: Use `:mask-closable="false"` for iView modals with input operations for consistent UX.